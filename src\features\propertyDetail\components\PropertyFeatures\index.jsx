import {
  FiCheckCircle,
  FiHome,
  FiCalendar,
  FiMapPin,
  FiLayers,
  FiDownload,
} from "react-icons/fi";

function PropertyFeatures({ property }) {
  if (!property) return null;

  const features = [
    {
      icon: <FiHome className="text-primary text-2xl" />,
      label: "Property Type",
      value: property.type,
    },
    {
      icon: <FiCalendar className="text-primary text-2xl" />,
      label: "Handover Date",
      value: property.handover,
    },
    property.location && {
      icon: <FiMapPin className="text-primary text-2xl" />,
      label: "Location",
      value: `${property.location.area}, ${property.location.city}`,
    },
    {
      icon: <FiLayers className="text-primary text-2xl" />,
      label: "Available Unit Types",
      value: `${property.unit_types?.length || 0} types`,
    },
  ].filter(Boolean);

  return (
    <section className="mb-10 h-full">
      <div className="bg-white rounded-2xl shadow-xl p-8 h-full flex flex-col justify-between">

        {/* Title */}
        <div className="flex items-center gap-3 mb-6">
          <FiCheckCircle className="text-3xl text-primary" />
          <h3 className="text-3xl font-bold text-primary">Property Features</h3>
        </div>

        {/* Feature Grid */}
        <div className="space-y-4">
          {features.map((feature, index) => (
            <div
              key={index}
              className="flex items-center gap-4 bg-gray-50 rounded-lg px-5 py-4"
            >
              <div className="w-8">{feature.icon}</div>
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between w-full">
                <span className="font-medium text-gray-700 text-base sm:text-lg">
                  {feature.label}
                </span>
                <span className="text-primary font-semibold text-lg mt-1 sm:mt-0">
                  {feature.value}
                </span>
              </div>
            </div>
          ))}

          {/* Brochure Button */}
          {property.brochure && (
            <a
              href={property.brochure}
              target="_blank"
              rel="noopener noreferrer"
              className="mt-4 block bg-primary hover:bg-primary/90 text-white rounded-xl px-6 py-4 text-center font-bold text-lg shadow-lg transition-all"
            >
              <div className="flex items-center justify-center gap-3">
                <FiDownload className="text-xl" />
                <span>Download Full Property Brochure (PDF)</span>
              </div>
            </a>
          )}
        </div>
      </div>
    </section>
  );
}

export default PropertyFeatures;
