import { useState, useRef } from "react";
import CarouselCard from "./components/CarouselCard";
import CarouselController from "./components/CarouselController";
import Card from "./../Card/Card";

const Carousel = ({ carouselData, itemsPerIndex = 3, isDark = false }) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const scrollRef = useRef(null);

  // Handle horizontal scroll on mobile
  const handleScroll = (direction) => {
    if (scrollRef.current) {
      const scrollAmount = 300; // Adjust scroll distance as needed
      const currentScroll = scrollRef.current.scrollLeft;
      const newScroll =
        direction === "left"
          ? currentScroll - scrollAmount
          : currentScroll + scrollAmount;

      scrollRef.current.scrollTo({
        left: newScroll,
        behavior: "smooth",
      });
    }
  };

  return (
    <div className="w-full mx-auto">
      {/* Carousel Items */}
      {/* Desktop: Grid layout */}
      <div className="hidden md:grid md:grid-cols-3 gap-6 mb-6">
        {carouselData
          .filter(
            (_, index) =>
              index >= itemsPerIndex * activeIndex &&
              index <= itemsPerIndex * activeIndex + (itemsPerIndex - 1)
          )
          .map((item) => (
            <Card
              key={item.id}
              imgSrc={item.image}
              title={item.title}
              price={item.price}
              subtitle={item.description}
              isDark={isDark}
            />
          ))}
      </div>

      {/* Mobile: Horizontal scroll */}
      <div className="md:hidden mb-6">
        <div
          ref={scrollRef}
          className="flex gap-6 overflow-x-auto scrollbar-hide pb-4 px-4"
          style={{ scrollSnapType: "x mandatory" }}
        >
          {carouselData.map((item) => (
            <div
              key={item.id}
              className="flex-shrink-0 w-[calc(100vw-3rem)]"
              style={{ scrollSnapAlign: "center" }}
            >
              <Card
                imgSrc={item.image}
                title={item.title}
                price={item.price}
                subtitle={item.description}
                isDark={isDark}
              />
            </div>
          ))}
        </div>
      </div>

      {/* Custom Sliding Controller - Only show on desktop */}
      <div className="hidden md:block">
        <CarouselController
          totalItems={carouselData.length}
          activeIndex={activeIndex}
          onIndexChange={setActiveIndex}
          itemsPerIndex={itemsPerIndex}
        />
      </div>

      {/* Mobile Navigation Arrows */}
      <div className="md:hidden flex justify-center gap-4 mt-4">
        <button
          onClick={() => handleScroll("left")}
          className="p-3 rounded-full bg-primary text-white hover:bg-primary/80 transition-colors"
          aria-label="Scroll left"
        >
          ←
        </button>
        <button
          onClick={() => handleScroll("right")}
          className="p-3 rounded-full bg-primary text-white hover:bg-primary/80 transition-colors"
          aria-label="Scroll right"
        >
          →
        </button>
      </div>
    </div>
  );
};

export default Carousel;
