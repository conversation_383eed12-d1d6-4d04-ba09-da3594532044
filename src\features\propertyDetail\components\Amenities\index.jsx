import { FiCheckCircle } from "react-icons/fi";

function Amenities({ property }) {
  if (!property?.amenities || property.amenities.length === 0) {
    return null;
  }

  return (
    <section className="h-full">
      <div className="bg-white rounded-2xl shadow-xl p-8 h-full flex flex-col justify-between">
        {/* Header */}
        <div className="flex items-center gap-2 border-b-2 border-primary pb-3 mb-6">
          <FiCheckCircle className="text-2xl text-primary" />
          <h3 className="text-2xl font-bold text-primary">Amenities</h3>
        </div>

        {/* Amenities List */}
        <ul className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-base md:text-lg text-gray-800 flex-1">
          {property.amenities.map((amenity, index) => (
            <li
              key={index}
              className="flex items-start gap-2 bg-gray-50 px-4 py-2 rounded-lg"
            >
              <FiCheckCircle className="text-primary mt-1 shrink-0" />
              <span className="leading-snug">{amenity}</span>
            </li>
          ))}
        </ul>
      </div>
    </section>
  );
}

export default Amenities;
