import Carousel from "../../../../components/Carousel";
import Subtitle from "./../../../../components/Subtitle/index";
const firstImg = "/images/CSP_1.webp";
const secondImg = "/images/CSP_2.webp";
const thirdImg = "/images/CSP_3.webp";

const carouselData = [
  {
    id: 1,
    title: "Oupla Residences",

    description: "City Walk, Dubai",
    image: firstImg,
    color: "from-blue-500 to-purple-600",
  },
  {
    id: 2,
    title: "Oupla Residences",

    description: "City Walk, Dubai",
    image: secondImg,
    color: "from-emerald-500 to-teal-600",
  },
  {
    id: 3,
    title: "Oupla Residences",

    description: "City Walk, Dubai",
    image: thirdImg,
    color: "from-orange-500 to-red-600",
  },
  {
    id: 4,
    title: "Oupla Residences",

    description: "City Walk, Dubai",
    image: secondImg,
    color: "from-orange-500 to-red-600",
  },
  {
    id: 5,
    title: "Oupla Residences",

    description: "City Walk, Dubai",
    image: firstImg,
    color: "from-orange-500 to-red-600",
  },
  {
    id: 6,
    title: "Oupla Residences",

    description: "City Walk, Dubai",
    image: thirdImg,
    color: "from-orange-500 to-red-600",
  },
  {
    id: 7,
    title: "Oupla Residences",

    description: "City Walk, Dubai",
    image: thirdImg,
    color: "from-orange-500 to-red-600",
  },
  {
    id: 8,
    title: "Oupla Residences",

    description: "City Walk, Dubai",
    image: firstImg,
    color: "from-orange-500 to-red-600",
  },
  {
    id: 9,
    title: "Oupla Residences",

    description: "City Walk, Dubai",
    image: secondImg,
    color: "from-orange-500 to-red-600",
  },
  {
    id: 10,
    title: "Oupla Residences",

    description: "City Walk, Dubai",
    image: firstImg,
    color: "from-orange-500 to-red-600",
  },
];

function CuratedSecondaryProperties() {
  return (
    <section className="py-20 bg-[#EBE6E2] text-secondary">
      <div className="mx-auto px-6 md:px-30">
        <Subtitle text="Our hot deals" />
        <div className="grid md:grid-cols-12 gap-5">
          <h2 className="md:col-span-8 text-5xl">
            Curated Secondary Properties
          </h2>
          {/* <div className="md:col-span-4 justify-self-end">
            <a className="text-lg cursor-pointer text-[#bea15d]">
              <span className="underline underline-offset-3">Dubai</span>
              <span className="font-extrabold ml-3">→</span>
            </a>
          </div> */}
        </div>

        <div className="mt-10">
          <Carousel carouselData={carouselData} />
        </div>
      </div>
    </section>
  );
}

export default CuratedSecondaryProperties;
