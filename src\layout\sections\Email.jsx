const logoSrc = "/images/Logo.webp";
const telegramIcon = "/icons/telegram.svg";
const whatsappIcon = "/icons/whatsapp.svg";
import { useState } from "react";

const socialIcons = [
  { icon: telegramIcon, href: "https://www.whatsapp.com", alt: "Telegram" },
  { icon: whatsappIcon, href: "https://www.telegram.org", alt: "WhatsApp" },
];

function Email() {
  const [form, setForm] = useState([
    { type: "text", label: "Name", value: "" },
    { type: "text", label: "Phone", value: "" },
    { type: "email", label: "Email", value: "" },
  ]);

  function handleFormChange(e) {
    setForm((oldForm) =>
      oldForm.map((formInput) =>
        e.target.id === formInput.label
          ? { ...formInput, value: e.target.value }
          : formInput
      )
    );
  }

  return (
    <section className="py-10 bg-secondary text-[#12253F] ">
      <div className="mx-auto px-6 md:px-60  ">
        <div className="bg-[#F7F0EB] p-10 text-center flex justify-center flex-col items-center gap-5 rounded-xl  lg:h-[50rem]">
          <img
            className="size-30 text-center"
            src={logoSrc}
            alt="LUX Investment Logo"
          />

          <p className="text-6xl font-medium max-w-3xl">
            “Let <span className="font-bold italic">the experts</span> help you
            make
            <span className="font-bold italic"> the right investment</span>”
          </p>

          <div className="self-start text-start px-15 mt-16 w-full font-sans">
            <ul>
              {socialIcons.map(({ icon, href, alt }) => (
                <li key={alt} className="inline-block mr-10 mt-2.5">
                  <a href={href} target="_blank" rel="noopener noreferrer">
                    <span className="mr-3 inline-flex items-center justify-center bg-primary p-3 rounded-full">
                      <img src={icon} alt={alt} className="block size-5" />
                    </span>
                    <span className="">{alt}</span>
                  </a>
                </li>
              ))}
            </ul>

            <form
              onSubmit={(e) => e.preventDefault()}
              className="font-outfit mt-8 bg-[#EEE7DF] w-full p-8 flex flex-col md:flex-row gap-4 justify-between md:items-center"
            >
              <div className="grid gap-6 md:grid-cols-3">
                {form.map(({ type, label, value }) => (
                  <TextInput
                    key={label}
                    type={type}
                    label={label}
                    value={value}
                    handleFormChange={handleFormChange}
                  />
                ))}
              </div>
              <button
                type="submit"
                className="bg-primary text-xl font-extralight hover:bg-blue-700 text-white py-2.5 px-16 rounded-full"
              >
                Get Consultation
              </button>
            </form>
          </div>
        </div>
      </div>
    </section>
  );
}

function TextInput({ label, type, value, handleFormChange }) {
  return (
    <div className="relative lg:min-w-60">
      <input
        required
        type={type || "text"}
        id={label}
        onChange={handleFormChange}
        value={value}
        placeholder=" "
        className="peer w-full border-0 border-b-2 border-gray-300 bg-transparent px-0 py-2 text-sm text-gray-900 placeholder-transparent focus:border-blue-600 focus:outline-none"
      />
      <label
        htmlFor={label}
        className="absolute left-0 top-[-3px] text-sm text-gray-500 transition-all peer-placeholder-shown:top-2.5 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-400 peer-focus:text-blue-600"
      >
        {label}
      </label>
    </div>
  );
}

export default Email;
