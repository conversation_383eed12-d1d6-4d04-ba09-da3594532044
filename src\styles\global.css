@import url("https://fonts.googleapis.com/css2?family=Outfit:wght@100;200;300;400;500;600;700;800;900&family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;0,800;0,900;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");
@import "./theme/theme.css";

@import "tailwindcss/utilities";
@import "tailwindcss";
@layer utilities {
  .text-primary {
    color: #bea15d;
  }
  .text-secondary {
    color: #12253f;
  }
  .text-gray {
    color: #878787;
  }
  .bg-primary {
    background-color: #bea15d !important;
  }
  .hover\:bg-primary:hover {
    background-color: #bea15d !important;
    border-color: #bea15d !important;
  }
  .bg-secondary {
    background-color: #12253f;
  }
  .font-playfair {
    font-family: "Playfair Display", serif;
  }
  .font-outfit {
    font-family: "Outfit", sans-serif;
  }
}

/* Common CSS */
html {
  scroll-behavior: smooth;
}

.line {
  /* Line */
  width: 5rem;
  height: 0px;
  border: 0.1px solid #bea15d;
  vertical-align: middle;

  /* Inside auto layout */
  flex: none;
  order: 0;
  flex-grow: 0;
}

/* CSS Reset */
*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
}

html {
  font-size: 62.5%; /* Now 1rem = 10px */
  line-height: 1.15; /* Improve text rendering */
  -webkit-text-size-adjust: 100%; /* Prevent adjustments of font size after orientation changes in iOS */
}

body {
  margin: 0;
  padding: 0;
  font-family: "Playfair Display", serif, "Inter", sans-serif; /* Playfair Display as default body font */
  font-size: 1.6rem; /* = 16px */
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Rest of your CSS remains the same... */
/* Remove list styles on ul, ol elements with a list role */
ul[role="list"],
ol[role="list"] {
  list-style: none;
}

/* Remove default list styles */
ul,
ol {
  list-style: none;
  margin: 0;
  padding: 0;
}

/* Set core root defaults */
html:focus-within {
  scroll-behavior: smooth;
}

/* Remove all animations, transitions and smooth scroll for people that prefer not to see them */
@media (prefers-reduced-motion: reduce) {
  html:focus-within {
    scroll-behavior: smooth;
  }

  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: smooth !important;
  }
}

/* Make images easier to work with */
img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
  height: auto;
}

/* Inherit fonts for inputs and buttons */
input,
button,
textarea,
select {
  font: inherit;
}

/* Remove default button styles */
button {
  border: none;
  background: none;
  cursor: pointer;
}

/* Remove default anchor styles */
a {
  text-decoration: none;
  color: inherit;
}

/* Improve table rendering */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* Remove default fieldset styles */
fieldset {
  border: none;
  margin: 0;
  padding: 0;
}

/* Remove default legend styles */
legend {
  padding: 0;
}
@layer utilities {
  @keyframes scrollLeft {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(-50%);
    }
  }

  .animate-scrollLeft {
    animation: scrollLeft 25s linear infinite;
  }
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}