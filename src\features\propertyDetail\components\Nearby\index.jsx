import { FiMapPin, FiDollarSign } from "react-icons/fi";

function Nearby({ property }) {
  if (!property?.location) return null;

  return (
    <>
      {/* Location & Nearby */}
      <section className="bg-white rounded-3xl shadow-2xl p-10 mb-12  mx-auto">
        <div className="flex items-center gap-4 border-b-4 border-primary pb-5 mb-8">
          <FiMapPin className="text-3xl text-primary" />
          <h3 className="text-3xl font-extrabold text-primary tracking-wide">
            Location & Nearby
          </h3>
        </div>

        <div className="space-y-6 text-gray-900 text-xl font-medium">
          <p>
            <span className="font-bold text-primary">Area:</span> {property.location.area}
          </p>
          <p>
            <span className="font-bold text-primary">City:</span> {property.location.city}
          </p>

          {property.location?.nearby?.length > 0 && (
            <div>
              <h4 className="text-2xl font-semibold mb-4 text-primary">
                Nearby Places
              </h4>
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
                {property.location.nearby.map((place, i) => (
                  <div
                    key={i}
                    className="flex items-center gap-3 bg-primary/10 rounded-xl shadow-md p-5 hover:bg-primary/20 transition cursor-default"
                  >
                    <FiMapPin className="text-primary text-2xl" />
                    <span className="text-lg font-semibold">{place}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </section>

      {/* Payment Plan */}
      {property.payment_plan && (
        <section className="bg-white rounded-3xl shadow-2xl p-10  mx-auto mb-12">
          <div className="flex items-center gap-4 border-b-4 border-primary pb-5 mb-8">
            <FiDollarSign className="text-3xl text-primary" />
            <h3 className="text-3xl font-extrabold text-primary tracking-wide">
              Payment Plan
            </h3>
          </div>

          <p className="text-xl text-gray-800 font-semibold mb-8">
            {property.payment_plan.summary}
          </p>

          <div className="grid grid-cols-1 sm:grid-cols-3 gap-10 text-center">
            {[
              {
                label: "Down Payment",
                value: property.payment_plan.details.down_payment_percent,
              },
              {
                label: "During Construction",
                value: property.payment_plan.details.during_construction_percent,
              },
              {
                label: "Post-Handover",
                value: property.payment_plan.details.post_handover_percent,
              },
            ].map(({ label, value }, idx) => (
              <div
                key={idx}
                className="bg-primary/20 rounded-2xl p-8 shadow-lg flex flex-col items-center justify-center"
              >
                <div className="text-primary text-2xl font-semibold mb-2">{label}</div>
                <div className="text-4xl font-extrabold text-gray-900">{value}%</div>
              </div>
            ))}
          </div>

          {property.payment_plan.details.monthly_installments && (
            <p className="mt-10 text-center text-lg text-gray-700 font-medium">
              Monthly Installments for{" "}
              <span className="font-bold text-gray-900">
                {property.payment_plan.details.total_months}
              </span>{" "}
              months
            </p>
          )}
        </section>
      )}
    </>
  );
}

export default Nearby;
