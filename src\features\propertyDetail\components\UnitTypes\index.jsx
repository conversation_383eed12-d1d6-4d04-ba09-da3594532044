import { FiLayers, FiDollarSign } from "react-icons/fi";

function UnitTypes({ property }) {
  if (!property?.unit_types || property.unit_types.length === 0) return null;

//   const mainUnit = property.unit_types[0];
//   const startingPrice = mainUnit
//     ? `AED ${mainUnit.starting_price.toLocaleString()}`
//     : "N/A";

  return (
    <section className="bg-white rounded-2xl shadow-md p-6 md:p-8 mb-10 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <FiLayers className="text-3xl text-primary" />
        <h3 className="text-2xl md:text-3xl font-bold text-primary">
          Unit Types & Pricing
        </h3>
      </div>

      {/* Price Card */}
      {/* <div className="bg-gradient-to-br from-primary to-primary/90 text-white px-6 py-5 rounded-xl flex items-center justify-between">
        <div className="flex items-center gap-3">
          <FiDollarSign className="text-2xl" />
          <span className="text-lg md:text-xl font-semibold">Starting From</span>
        </div>
        <div className="text-3xl font-extrabold">{startingPrice}</div>
      </div> */}

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full text-base md:text-lg">
          <thead>
            <tr className="border-b-2 border-primary text-left">
              <th className="py-4 pr-8 font-semibold text-primary">Type</th>
              <th className="py-4 pr-8 font-semibold text-primary">Size (sqft)</th>
              <th className="py-4 pr-8 font-semibold text-primary">Starting Price</th>
            </tr>
          </thead>
          <tbody>
            {property.unit_types.map((unit, index) => (
              <tr
                key={unit._id}
                className={`border-b last:border-b-0 ${
                  index % 2 === 0 ? "bg-gray-50/50" : ""
                } hover:bg-gray-50 transition`}
              >
                <td className="py-4 pr-8 font-medium text-xl">{unit.type}</td>
                <td className="py-4 pr-8 text-xl">{unit.size_range_sqft}</td>
                <td className="py-4 pr-8 font-semibold text-primary text-xl">
                  AED {unit.starting_price.toLocaleString()}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </section>
  );
}

export default UnitTypes;
