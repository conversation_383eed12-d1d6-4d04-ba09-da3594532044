import { NavLink } from "react-router-dom";
import styles from "./Header.module.css";
// Import social media icons
const linkedinIcon = "/icons/linkedin.svg";
const facebookIcon = "/icons/facebook.svg";
const instagramIcon = "/icons/instagram.svg";

const links = [
  { to: "/", label: "Home" },
  { to: "/about", label: "About Us" },
  // { to: '/services',  label: 'Our Services' },
  { to: "/destinations", label: "Destinations" },
  // { to: '/news',      label: 'News' },
];

const socialIcons = [
  { icon: linkedinIcon, href: "https://www.linkedin.com", alt: "LinkedIn" },
  { icon: facebookIcon, href: "https://www.facebook.com", alt: "Facebook" },
  { icon: instagramIcon, href: "https://www.instagram.com", alt: "Instagram" },
];

export default function NavLinks() {
  return (
    <div className={styles.navContainer}>
      {/* Navigation Links - Left Side */}
      <ul className={styles.linkList}>
        {links.map(({ to, label }) => (
          <li key={to}>
            <NavLink
              to={to}
              className={({ isActive }) =>
                isActive ? styles.activeLink : styles.link
              }
            >
              {label}
            </NavLink>
          </li>
        ))}
      </ul>

      {/* Social Icons - Right Side */}
      <ul className={styles.socialList}>
        {socialIcons.map(({ icon, href, alt }, i) => (
          <li key={i}>
            <a
              href={href}
              target="_blank"
              rel="noopener noreferrer"
              className={styles.socialLink}
            >
              <img src={icon} alt={alt} className={styles.socialIcon} />
            </a>
          </li>
        ))}
      </ul>
    </div>
  );
}
