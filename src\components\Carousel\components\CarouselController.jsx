const CarouselController = ({
  totalItems,
  activeIndex,
  onIndexChange,
  itemsPerIndex,
}) => {
  const totalIndices = Math.ceil(totalItems / itemsPerIndex);

  return (
    <div className="flex flex-col sm:flex-row justify-between gap-4 sm:gap-8 items-center">
      <p className="font-sans text-base sm:text-lg text-gray order-2 sm:order-1">
        <span className="text-primary">0{activeIndex + 1} </span>/ 0
        {totalIndices}
      </p>

      <div className="flex-1 h-1 sm:h-0.5 py-1 sm:py-0.5 relative bg-[#D3D1D0] shadow-inner order-1 sm:order-2 w-full sm:w-auto">
        {/* Background Track */}
        <div className="flex">
          {Array.from({ length: totalIndices }).map((_, index) => (
            <button
              key={index}
              onClick={() => onIndexChange(index)}
              className="absolute -top-[.001rem] z-10 h-2 sm:h-1 hover:bg-white/20 touch-manipulation"
              style={{
                left: `calc((100%/${totalIndices})*${index})`,
                width: `calc(100%/${totalIndices})`,
              }}
            />
          ))}
        </div>

        {/* Sliding Indicator */}
        <div
          className="absolute -top-[.001rem] h-2 sm:h-1 bg-primary shadow-lg transition-all duration-300"
          style={{
            transform: `translateX(calc(((100%/${totalIndices})*${activeIndex})*${totalIndices}))`,
            width: `calc(100%/${totalIndices})`,
          }}
        />
      </div>

      <div className="text-primary order-3 flex gap-2">
        <button
          className="font-extrabold p-2 sm:p-1 cursor-pointer hover:bg-primary/10 rounded-full transition-colors touch-manipulation"
          onClick={() =>
            onIndexChange((activeIndex - 1 + totalIndices) % totalIndices)
          }
          aria-label="Previous slide"
        >
          ←
        </button>
        <button
          className="font-extrabold p-2 sm:p-1 cursor-pointer hover:bg-primary/10 rounded-full transition-colors touch-manipulation"
          onClick={() => onIndexChange((activeIndex + 1) % totalIndices)}
          aria-label="Next slide"
        >
          →
        </button>
      </div>
    </div>
  );
};

export default CarouselController;
