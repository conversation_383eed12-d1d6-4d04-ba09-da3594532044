import { FiHome, FiStar, FiInfo, FiCalendar } from "react-icons/fi";

function MainInfo({ property }) {
  if (!property) return null;

  return (
    <section className="bg-white rounded-2xl shadow-lg p-6 md:p-8 h-full flex flex-col justify-between">
      <div>
        {/* Title */}
        <div className="flex items-center gap-3 mb-4">
          <FiHome className="text-3xl text-primary" />
          <h1 className="text-3xl md:text-4xl font-extrabold text-gray-900">
            {property.title}
          </h1>
        </div>

        {/* Subtitle */}
        {property.subtitle && (
          <div className="flex items-center gap-3 mb-5">
            <FiStar className="text-2xl text-primary" />
            <p className="text-xl text-primary font-semibold italic">
              {property.subtitle}
            </p>
          </div>
        )}

        {/* Summary */}
        {property.summary && (
          <div className="flex items-start gap-3 mb-5">
            <FiInfo className="text-xl text-primary mt-1" />
            <p className="text-base md:text-lg text-gray-700 leading-relaxed">
              {property.summary}
            </p>
          </div>
        )}

        {/* Description */}
        {property.desc && (
          <div className="flex items-start gap-3 mb-6">
            <FiInfo className="text-xl text-primary mt-1" />
            <p className="text-base md:text-lg text-gray-700 leading-relaxed">
              {property.desc}
            </p>
          </div>
        )}
      </div>

      {/* Tags */}
      <div className="mt-auto">
        <div className="flex flex-wrap gap-3 pt-4 border-t border-gray-100">
          {property.type && (
            <span className="inline-flex items-center gap-2 bg-primary text-white px-5 py-2 rounded-full text-sm md:text-base font-medium">
              <FiHome className="text-base" />
              {property.type}
            </span>
          )}

          {property.handover && (
            <span className="inline-flex items-center gap-2 bg-primary text-white px-5 py-2 rounded-full text-sm md:text-base font-medium">
              <FiCalendar className="text-base" />
              Handover: {property.handover}
            </span>
          )}
        </div>
      </div>
    </section>
  );
}

export default MainInfo;
