import styles from "./Header.module.css";
// Import PNG images as default imports
const linkedinIcon = "/icons/linkedin.svg";
const facebookIcon = "/icons/facebook.svg";
const instagramIcon = "/icons/instagram.svg";
const icons = [
  { icon: linkedinIcon, href: "https://www.linkedin.com", alt: "LinkedIn" },
  { icon: facebookIcon, href: "https://www.facebook.com", alt: "Facebook" },
  { icon: instagramIcon, href: "https://www.instagram.com", alt: "Instagram" },
];

export default function SocialIcons() {
  return (
    <ul className={styles.socialList}>
      {icons.map(({ icon, href, alt }, i) => (
        <li key={i}>
          <a href={href} target="_blank" rel="noopener noreferrer">
            <img src={icon} alt={alt} className={styles.socialIcon} />
          </a>
        </li>
      ))}
    </ul>
  );
}
